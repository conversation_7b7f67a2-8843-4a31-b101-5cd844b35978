"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>ger,
} from "@askinfosec/shadcn-ui/components/ui/sheet";
import { Button } from "@askinfosec/shadcn-ui/components/ui/button";
import { Edit, PanelLeft, X } from "lucide-react";
import { DocumentEditor } from "./document-editor";
import { ContractsV1Files } from "@askinfosec/types";

interface DocumentEditorContainerProps {
  document: ContractsV1Files.FileDTO;
  organizationId: string;
  onSave?: (updatedDocument: Partial<ContractsV1Files.FileDTO>) => void;
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export const DocumentEditorContainer = ({
  document,
  organizationId,
  onSave,
  trigger,
  open,
  onOpenChange,
}: DocumentEditorContainerProps) => {
  const [isO<PERSON>, setIsOpen] = useState(false);

  const handleOpenChange = (newOpen: boolean) => {
    if (onOpenChange) {
      onOpenChange(newOpen);
    } else {
      setIsOpen(newOpen);
    }
  };

  const handleSave = (updatedDocument: Partial<ContractsV1Files.FileDTO>) => {
    onSave?.(updatedDocument);
    handleOpenChange(false);
  };

  const handleCancel = () => {
    handleOpenChange(false);
  };

  const handlePanelClick = () => {
    handleOpenChange(true);
  };

  const isControlled = open !== undefined;
  const sheetOpen = isControlled ? open : isOpen;

  return (
    <Sheet open={sheetOpen} onOpenChange={handleOpenChange}>
      {!isControlled && (
        <SheetTrigger asChild>
          {trigger || (
            <Button variant="outline" size="sm">
              <Edit className="mr-2 h-4 w-4" />
              Edit Document
            </Button>
          )}
        </SheetTrigger>
      )}
      <SheetContent
        className="w-full sm:w-[65vw] max-w-4xl [&>button]:hidden"
        side="left"
      >
        <SheetHeader className="flex flex-row items-end justify-end">
          <X className="h-4 w-4 mr-2" onClick={() => handleOpenChange(false)} />
          <PanelLeft className="h-4 w-4" onClick={() => handlePanelClick()} />
          <SheetTitle className="sr-only">Edit Document</SheetTitle>
        </SheetHeader>
        <DocumentEditor
          document={document}
          organizationId={organizationId}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      </SheetContent>
    </Sheet>
  );
};
